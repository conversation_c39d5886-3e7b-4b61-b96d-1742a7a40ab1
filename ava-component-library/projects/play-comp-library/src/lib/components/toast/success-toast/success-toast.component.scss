.ava-toast {
    pointer-events: var(--toast-pointer-events);
    display: var(--toast-display);
    align-items: var(--toast-align-items);
    gap: var(--toast-gap);
    padding: var(--toast-padding);
    border-radius: var(--toast-border-radius);
    box-shadow: var(--toast-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid var(--toast-border);
    transition: var(--toast-transition);
    transform: translateX(100%);
    opacity: 0;
    max-width: var(--toast-max-width);
    min-width: var(--toast-min-width);
    position: var(--toast-position);
    overflow: var(--toast-overflow);
    cursor: var(--toast-cursor);
    margin-bottom: var(--toast-margin-bottom);

    &.show {
        transform: translateX(0);
        opacity: 1;
    }

    &.hide {
        transform: translateX(100%);
        opacity: 0;
        margin-top: -80px;
    }

    /* Success Toast Specific Styling */
    &.success {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.9));
        color: white;
        border-color: var(--toast-success-border);

        .toast-title,
        .toast-message {
            color: white;
        }
    }

    /* Default Toast (White Background with Black Text) */
    &.default {
        background: var(--toast-default-background);
        color: var(--toast-default-text);
        border-color: var(--toast-default-border);

        .toast-title {
            color: var(--toast-default-title-color);
            font-weight: var(--toast-title-weight);
        }

        .toast-message {
            color: var(--toast-default-message-color);
            font-weight: var(--toast-message-weight);
        }
    }

    /* Theme Variations */
    &.theme-light {
        background: var(--toast-default-background) !important;
        color: var(--toast-default-text) !important;
        border-color: var(--toast-default-border) !important;

        .toast-title {
            color: var(--toast-default-title-color) !important;
            font-weight: var(--toast-title-weight) !important;
        }

        .toast-message {
            color: var(--toast-default-message-color) !important;
            font-weight: var(--toast-message-weight) !important;
        }
    }

    &.theme-dark {
        background: var(--toast-background-dark);
        color: var(--toast-title-color-dark);
        border-color: var(--toast-border);
    }
}

/* Toast Icon */
.toast-icon {
    width: var(--toast-icon-width);
    height: var(--toast-icon-height);
    flex-shrink: var(--toast-icon-flex-shrink);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Toast Content */
.toast-content {
    flex: var(--toast-content-flex);
}

.toast-title {
    font-weight: var(--toast-title-weight);
    margin-bottom: var(--toast-title-margin-bottom);
    line-height: var(--toast-title-line-height);
    font-size: var(--toast-title-font);
    color: inherit;
}

.toast-message {
    font-weight: var(--toast-message-weight);
    font-size: var(--toast-message-font-size);
    line-height: var(--toast-message-line-height);
    color: inherit;
    opacity: var(--toast-message-opacity);
    margin-bottom: var(--toast-message-margin-bottom);
}

/* Toast Actions */
.toast-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

/* Close Button */
.toast-close {
    flex-shrink: 0;
}

/* Progress Bar */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0 0 var(--toast-border-radius) var(--toast-border-radius);
    transition: width linear;
}

/* Toast Actions */
.toast-actions {
    display: flex;
    gap: var(--global-spacing-2);
    margin-top: var(--global-spacing-2);
}
