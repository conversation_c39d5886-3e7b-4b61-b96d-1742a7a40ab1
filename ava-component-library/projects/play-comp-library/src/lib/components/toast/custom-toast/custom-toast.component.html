<div class="ava-toast custom"
     [ngClass]="{
         'show': isVisible,
         'hide': isExiting,
         'theme-light': theme === 'light',
         'theme-dark': theme === 'dark',
         'size-small': size === 'small',
         'size-medium': size === 'medium',
         'size-large': size === 'large',
         'type-default': type === 'default',
         'type-success': type === 'success',
         'type-error': type === 'error',
         'type-warning': type === 'warning',
         'type-info': type === 'info',
         'type-loading': type === 'loading'
     }"
     [ngStyle]="computedStyles"
     (click)="onToastClick()">
    
    <!-- Icon Section -->
    <div class="toast-icon" *ngIf="showIcon">
        <ava-icon
            [iconName]="displayIcon"
            [iconSize]="iconSize"
            [iconColor]="displayIconColor"
            aria-hidden="true">
        </ava-icon>

        <!-- Badge overlay on icon -->
        <ava-badges
            *ngIf="hasBadge"
            class="toast-badge"
            [count]="badge?.count"
            [iconName]="badge?.icon"
            [state]="badge?.state || 'neutral'"
            size="small">
        </ava-badges>
    </div>
    
    <!-- Content Section -->
    <div class="toast-content" *ngIf="hasContent">
        <div class="toast-title" *ngIf="title">{{ title }}</div>
        
        <!-- Message with HTML support -->
        <div class="toast-message" *ngIf="displayContent">
            <span *ngIf="!allowHtml">{{ displayContent }}</span>
            <span *ngIf="allowHtml" [innerHTML]="displayContent"></span>
        </div>
        
        <!-- Tags -->
        <div class="toast-tags" *ngIf="hasTags">
            <ava-tag
                *ngFor="let tag of tags"
                [label]="tag"
                color="primary"
                variant="outlined"
                size="sm"
                [removable]="true"
                (removed)="onTagRemove(tag)">
            </ava-tag>
        </div>

        <!-- Links -->
        <div class="toast-links" *ngIf="hasLinks">
            <ava-link
                *ngFor="let link of links"
                [label]="link.label"
                [href]="link.href || '#'"
                [color]="link.color || 'primary'"
                size="small"
                [underline]="true"
                (click)="onLinkClick(link)">
            </ava-link>
        </div>

        <!-- Custom Actions -->
        <div class="toast-actions" *ngIf="hasActions">
            <ava-button
                *ngFor="let action of actions"
                [label]="action.label"
                [variant]="action.variant || 'secondary'"
                [disabled]="action.disabled || false"
                [iconName]="action.icon || ''"
                [iconPosition]="action.iconPosition || 'left'"
                size="small"
                (userClick)="onActionClick(action)">
            </ava-button>
        </div>
    </div>
    
    <!-- Close Button -->
    <ava-button class="toast-close"
                *ngIf="closable"
                variant="secondary"
                [clear]="true"
                size="small"
                iconPosition="only"
                iconName="x"
                [iconSize]="16"
                (userClick)="onClose()"
                aria-label="Close toast">
    </ava-button>
    
    <!-- Progress Bar -->
    <div class="toast-progress" 
         *ngIf="showProgress && duration > 0"
         [style.width.%]="progressWidth">
    </div>
</div>
