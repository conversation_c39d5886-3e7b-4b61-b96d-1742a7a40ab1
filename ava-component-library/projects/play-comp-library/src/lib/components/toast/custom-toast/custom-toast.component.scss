/* Custom Toast Component - Uses existing _toast.css tokens */

.ava-toast.custom {
    display: flex;
    align-items: flex-start;
    gap: var(--global-spacing-3, 12px);
    background: var(--toast-background, #ffffff);
    border: 1px solid var(--toast-border, #e5e7eb);
    border-radius: var(--toast-border-radius, 8px);
    box-shadow: var(--toast-shadow, 0 4px 12px rgba(0, 0, 0, 0.15));
    backdrop-filter: blur(10px);
    padding: var(--toast-padding, 16px);
    max-width: var(--toast-max-width, 400px);
    min-width: var(--toast-min-width, 300px);
    position: relative;
    pointer-events: auto;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    /* Animation States */
    &.show {
        opacity: 1;
        transform: translateX(0);
    }

    &.hide {
        opacity: 0;
        transform: translateX(100%);
    }

    /* Bottom position animations */
    .ava-toast-container.position-bottom-left &,
    .ava-toast-container.position-bottom-center &,
    .ava-toast-container.position-bottom-right & {
        transform: translateY(100%);

        &.show {
            opacity: 1;
            transform: translateY(0);
        }

        &.hide {
            opacity: 0;
            transform: translateY(100%);
        }
    }

    /* Theme Variants */
    &.theme-dark {
        background: var(--toast-background-dark);
        
        .toast-title {
            font: var(--toast-title-font-dark);
            color: var(--toast-title-color-dark);
        }
        
        .toast-message {
            font: var(--toast-message-font-dark);
            color: var(--toast-message-color-dark);
        }
    }

    /* Size Variants */
    &.size-small {
        padding: var(--toast-size-sm-padding);
        max-width: var(--toast-size-sm-max-width);
        
        .toast-title,
        .toast-message {
            font: var(--toast-size-sm-font);
        }
    }

    &.size-medium {
        padding: var(--toast-size-md-padding);
        max-width: var(--toast-size-md-max-width);
        
        .toast-title,
        .toast-message {
            font: var(--toast-size-md-font);
        }
    }

    &.size-large {
        padding: var(--toast-size-lg-padding);
        max-width: var(--toast-size-lg-max-width);
        
        .toast-title,
        .toast-message {
            font: var(--toast-size-lg-font);
        }
    }

    /* Type Variants */
    &.type-default {
        background: var(--toast-default-background);
        border-color: var(--toast-default-border);

        .toast-title {
            color: var(--toast-default-title-color);
            font-weight: var(--toast-title-weight);
        }

        .toast-message {
            color: var(--toast-default-message-color);
            font-weight: var(--toast-message-weight);
        }
    }

    &.type-success {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.9));
        border-color: var(--toast-success-border, #10b981);

        .toast-title,
        .toast-message {
            color: white;
        }

        .toast-title {
            font-weight: var(--toast-title-weight, 600);
        }

        .toast-message {
            font-weight: var(--toast-message-weight, 400);
        }
    }

    &.type-error {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9));
        border-color: var(--toast-error-border, #ef4444);

        .toast-title,
        .toast-message {
            color: white;
        }

        .toast-title {
            font-weight: var(--toast-title-weight, 600);
        }

        .toast-message {
            font-weight: var(--toast-message-weight, 400);
        }
    }

    &.type-warning {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.9), rgba(217, 119, 6, 0.9));
        border-color: var(--toast-warning-border, #f59e0b);

        .toast-title,
        .toast-message {
            color: white;
        }

        .toast-title {
            font-weight: var(--toast-title-weight, 600);
        }

        .toast-message {
            font-weight: var(--toast-message-weight, 400);
        }
    }

    &.type-info {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(37, 99, 235, 0.9));
        border-color: var(--toast-info-border, #3b82f6);

        .toast-title,
        .toast-message {
            color: white;
        }

        .toast-title {
            font-weight: var(--toast-title-weight, 600);
        }

        .toast-message {
            font-weight: var(--toast-message-weight, 400);
        }
    }

    /* Icon Section */
    .toast-icon {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    /* Content Section */
    .toast-content {
        flex: 1;
        min-width: 0;
    }

    .toast-title {
        font: var(--toast-title-font, 14px system-ui);
        color: var(--toast-title-color, #111827);
        font-weight: var(--toast-title-weight, 600);
        line-height: var(--toast-title-line-height, 1.4);
        margin-bottom: var(--toast-title-margin-bottom, 4px);
    }

    .toast-message {
        font: var(--toast-message-font, 14px system-ui);
        color: var(--toast-message-color, #6b7280);
        font-weight: var(--toast-message-weight, 400);
        line-height: var(--toast-message-line-height, 1.5);
        margin-bottom: var(--toast-message-margin-bottom, 8px);
        word-wrap: break-word;

        &:last-child {
            margin-bottom: 0;
        }
    }

    /* Tags Section */
    .toast-tags {
        display: flex;
        gap: var(--global-spacing-1, 4px);
        flex-wrap: wrap;
        margin-top: var(--global-spacing-2, 8px);
        margin-bottom: var(--global-spacing-2, 8px);
    }

    /* Links Section */
    .toast-links {
        display: flex;
        gap: var(--global-spacing-3, 12px);
        flex-wrap: wrap;
        margin-top: var(--global-spacing-2, 8px);
        margin-bottom: var(--global-spacing-2, 8px);
    }

    /* Actions Section */
    .toast-actions {
        display: flex;
        gap: var(--global-spacing-2, 8px);
        flex-wrap: wrap;
        margin-top: var(--global-spacing-2, 8px);
    }

    /* Badge overlay */
    .toast-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        z-index: 1;
    }

    /* Close Button */
    .toast-close {
        position: absolute;
        top: var(--global-spacing-2);
        right: var(--global-spacing-2);
        flex-shrink: 0;
    }

    /* Progress Bar */
    .toast-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: currentColor;
        opacity: 0.3;
        transition: width linear;
        border-radius: 0 0 var(--toast-border-radius) var(--toast-border-radius);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        max-width: 100%;
        margin: 0 var(--global-spacing-2);
        
        .toast-actions {
            flex-direction: column;
            
            ava-button {
                width: 100%;
            }
        }
    }
}
