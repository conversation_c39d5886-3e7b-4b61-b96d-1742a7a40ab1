@import '../success-toast/success-toast.component.scss';

/* Error Toast Specific Styling */
.ava-toast.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9));
    color: white;
    border-color: var(--toast-error-border);

    .toast-title,
    .toast-message {
        color: white;
    }

    /* Theme override for light theme */
    &.theme-light {
        background: var(--toast-default-background) !important;
        color: var(--toast-default-text) !important;
        border-color: var(--toast-default-border) !important;

        .toast-title {
            color: var(--toast-default-title-color) !important;
            font-weight: var(--toast-title-weight) !important;
        }

        .toast-message {
            color: var(--toast-default-message-color) !important;
            font-weight: var(--toast-message-weight) !important;
        }
    }
}
