@import '../success-toast/success-toast.component.scss';

/* Warning Toast Specific Styling */
.ava-toast.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.9), rgba(217, 119, 6, 0.9));
    color: white;
    border-color: var(--toast-warning-border);

    .toast-title,
    .toast-message {
        color: white;
    }

    /* Theme override for light theme */
    &.theme-light {
        background: var(--toast-default-background) !important;
        color: var(--toast-default-text) !important;
        border-color: var(--toast-default-border) !important;

        .toast-title {
            color: var(--toast-default-title-color) !important;
            font-weight: var(--toast-title-weight) !important;
        }

        .toast-message {
            color: var(--toast-default-message-color) !important;
            font-weight: var(--toast-message-weight) !important;
        }
    }
}
