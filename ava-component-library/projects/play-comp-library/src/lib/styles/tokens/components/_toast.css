/**
 * =========================================================================
 * Play+ Design System: Toast Component Tokens
 *
 * Component-specific semantic tokens for toast notification elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for toast styling.
 * =========================================================================
 */

:root {
  /* --- Toast Base --- */
  --toast-background: var(--color-background-primary);
  --toast-background-dark: var(--global-color-senary);
  --toast-border: var(--color-border-default);
  --toast-border-radius: 12px;
  --toast-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  --toast-padding: 16px 20px;
  --toast-max-width: 400px;
  --toast-min-width: 300px;
  --toast-gap: 12px;
  --toast-display: flex;
  --toast-align-items: center;
  --toast-position: relative;
  --toast-overflow: hidden;
  --toast-pointer-events: auto;
  --toast-cursor: default;
  --toast-margin-bottom: 8px;

  /* --- Toast Title --- */
  --toast-title-font: var(--font-body-2);
  --toast-title-font-dark: var(--font-body-1);
  --toast-title-color: #111827;
  --toast-title-color-dark: var(--color-text-on-brand);
  --toast-title-weight: 600;
  --toast-title-line-height: 1.2;
  --toast-title-margin-bottom: 4px;

  /* --- Toast Message --- */
  --toast-message-font: var(--font-body-2);
  --toast-message-font-dark: var(--font-body-2);
  --toast-message-color: #6b7280;
  --toast-message-color-dark: var(--color-text-secondary);
  --toast-message-weight: 400;
  --toast-message-line-height: 1.4;
  --toast-message-margin-bottom: var(--global-spacing-3);
  --toast-message-opacity: 0.9;
  --toast-message-font-size: 0.9rem;

  /* --- Toast Link --- */
  --toast-link-font: var(--font-body-2);
  --toast-link-color: var(--color-text-interactive);
  --toast-link-color-hover: var(--color-text-interactive-hover);
  --toast-link-weight: var(--global-font-weight-medium);
  --toast-link-decoration: underline;
  --toast-link-decoration-hover: none;
  --toast-link-transition: var(--motion-pattern-fade);

  /* --- Toast Variants --- */
  --toast-success-background: var(--global-color-green-500);
  --toast-success-text: var(--color-text-on-brand);
  --toast-success-border: var(--global-color-green-500);

  --toast-error-background: var(--global-color-red-500);
  --toast-error-text: var(--color-text-on-brand);
  --toast-error-border: var(--global-color-red-500);

  --toast-warning-background: var(--global-color-yellow-500);
  --toast-warning-text: var(--global-color-black);
  --toast-warning-border: var(--global-color-yellow-500);

  --toast-info-background: var(--global-color-blue-info-500);
  --toast-info-text: var(--color-text-on-brand);
  --toast-info-border: var(--global-color-blue-info-500);

  --toast-loading-background: var(--global-color-blue-info-500);
  --toast-loading-text: var(--color-text-on-brand);
  --toast-loading-border: var(--global-color-blue-info-500);

  /* --- Loading Spinner --- */
  --toast-spinner-width: 20px;
  --toast-spinner-height: 20px;
  --toast-spinner-border: 2px solid rgba(255, 255, 255, 0.3);
  --toast-spinner-border-radius: 50%;
  --toast-spinner-border-top-color: currentColor;
  --toast-spinner-animation: spin 1s linear infinite;

  /* --- Loading Progress --- */
  --toast-loading-progress-margin-top: 8px;
  --toast-loading-progress-display: flex;
  --toast-loading-progress-align-items: center;
  --toast-loading-progress-gap: 8px;
  --toast-loading-progress-bar-height: 4px;
  --toast-loading-progress-bar-background: rgba(255, 255, 255, 0.3);
  --toast-loading-progress-bar-border-radius: 2px;
  --toast-loading-progress-bar-transition: width 0.3s ease;
  --toast-loading-progress-bar-flex: 1;
  --toast-loading-progress-text-font-size: 0.75rem;
  --toast-loading-progress-text-opacity: 0.8;
  --toast-loading-progress-text-min-width: 30px;
  --toast-loading-progress-text-text-align: right;

  /* --- Toast Sizes --- */
  --toast-size-sm-padding: var(--global-spacing-3);
  --toast-size-sm-font: var(--font-label);
  --toast-size-sm-max-width: 300px;

  --toast-size-md-padding: var(--global-spacing-4);
  --toast-size-md-font: var(--font-body-2);
  --toast-size-md-max-width: 400px;

  --toast-size-lg-padding: var(--global-spacing-5);
  --toast-size-lg-font: var(--font-body-1);
  --toast-size-lg-max-width: 500px;

  /* --- Toast Animation --- */
  --toast-transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  --toast-animation-duration: var(--global-motion-duration-standard);
  --toast-animation-easing: var(--global-motion-easing-enter);

  /* --- Toast Icon --- */
  --toast-icon-width: 24px;
  --toast-icon-height: 24px;
  --toast-icon-flex-shrink: 0;

  /* --- Toast Content --- */
  --toast-content-flex: 1;

  /* --- Toast Container --- */
  --toast-container-position: fixed;
  --toast-container-top: 20px;
  --toast-container-right: 20px;
  --toast-container-z-index: 10000;
  --toast-container-gap: 8px;

  /* --- Toast Mobile --- */
  --toast-mobile-max-width: calc(100vw - 40px);
  --toast-mobile-left: 20px;
  --toast-mobile-right: 20px;
  --toast-mobile-transform: none;

  /* --- Default Toast (White Background with Black Text) --- */
  --toast-default-background: rgba(255, 255, 255, 0.95);
  --toast-default-text: #374151;
  --toast-default-border: rgba(255, 255, 255, 0.2);
  --toast-default-title-color: #111827;
  --toast-default-message-color: #6b7280;
}